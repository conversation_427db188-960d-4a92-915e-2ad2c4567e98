'use client';

import { useState, useEffect } from 'react';
import { Image, Box } from '@mantine/core';
import { fetchData } from '../../lib/supabase';
import { AssetTemplate } from '../../lib/assets-utils';

interface AssetDisplayProps {
  imageUrl: string;
  templateId?: string | null;
  title: string;
  width?: number;
  height?: number;
  fit?: 'contain' | 'cover' | 'fill' | 'scale-down';
}

export default function AssetDisplay({ 
  imageUrl, 
  templateId, 
  title, 
  width = 200, 
  height = 200,
  fit = 'cover'
}: AssetDisplayProps) {
  const [template, setTemplate] = useState<AssetTemplate | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchTemplate = async () => {
      if (!templateId) return;

      try {
        setLoading(true);
        const { data, error } = await fetchData('asset_templates', {
          select: '*',
          filter: [{ column: 'id', value: templateId }],
          single: true
        });

        if (!error && data) {
          const templateData = Array.isArray(data) ? data[0] : data;
          setTemplate(templateData);
        }
      } catch (error) {
        console.error('Error fetching template:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplate();
  }, [templateId]);

  // If no template, just show the raw image
  if (!templateId || !template) {
    return (
      <Image
        src={imageUrl}
        alt={title}
        width={width}
        height={height}
        fit={fit}
      />
    );
  }

  // If template exists, render with template overlay
  return (
    <Box style={{ position: 'relative', width, height }}>
      {/* Template SVG as background */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundImage: template.preview_url ? `url(${template.preview_url})` : 'none',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          zIndex: 1
        }}
      />
      
      {/* Asset image positioned according to template data */}
      {template.template_data && typeof template.template_data === 'object' && 'imageArea' in template.template_data && (
        <div
          style={{
            position: 'absolute',
            zIndex: 2,
            // Calculate position based on template dimensions and container size
            left: `${((template.template_data as any).imageArea.x / (template.template_data as any).dimensions.width) * 100}%`,
            top: `${((template.template_data as any).imageArea.y / (template.template_data as any).dimensions.height) * 100}%`,
            width: `${((template.template_data as any).imageArea.width / (template.template_data as any).dimensions.width) * 100}%`,
            height: `${((template.template_data as any).imageArea.height / (template.template_data as any).dimensions.height) * 100}%`,
            borderRadius: (template.template_data as any).imageArea.shape === 'circle' ? '50%' : '0',
            overflow: 'hidden'
          }}
        >
          <Image
            src={imageUrl}
            alt={title}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        </div>
      )}
    </Box>
  );
}
