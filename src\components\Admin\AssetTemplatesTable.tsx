'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Text,
  Badge,
  Group,
  ActionIcon,
  Button,
  Modal,
  TextInput,
  Textarea,
  Select,
  JsonInput,
  Stack,
  Paper,
  Image,
  Card,
  Grid,
  Alert,
  LoadingOverlay,
  ScrollArea,
  Tooltip
} from '@mantine/core';
import {
  IconEdit,
  IconTrash,
  IconPlus,
  IconEye,
  IconTemplate,
  IconAlertCircle
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { modals } from '@mantine/modals';
import { fetchData, insertData, updateData, deleteData } from '../../lib/supabase';
import { AssetTemplate, ASSET_TYPES, getAssetTypeColor } from '../../lib/assets-utils';

interface TemplateFormData {
  id: string;
  name: string;
  description: string;
  asset_type: string;
  template_data: string;
  preview_url: string;
  is_active: boolean;
}

export function AssetTemplatesTable() {
  const [templates, setTemplates] = useState<AssetTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [modalOpened, setModalOpened] = useState(false);
  const [previewModalOpened, setPreviewModalOpened] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<AssetTemplate | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<AssetTemplate | null>(null);
  const [formData, setFormData] = useState<TemplateFormData>({
    id: '',
    name: '',
    description: '',
    asset_type: '',
    template_data: '{}',
    preview_url: '',
    is_active: true
  });

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const { data, error } = await fetchData('asset_templates', {
        select: '*',
        order: { column: 'created_at', ascending: false }
      });

      if (!error && data) {
        const templatesArray = Array.isArray(data) ? data : [data];
        setTemplates(templatesArray);
      } else if (error) {
        console.error('Error fetching templates:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to fetch asset templates',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to fetch asset templates',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingTemplate(null);
    setFormData({
      id: '',
      name: '',
      description: '',
      asset_type: '',
      template_data: '{}',
      preview_url: '',
      is_active: true
    });
    setModalOpened(true);
  };

  const handleEdit = (template: AssetTemplate) => {
    setEditingTemplate(template);
    setFormData({
      id: template.id,
      name: template.name,
      description: template.description || '',
      asset_type: template.asset_type,
      template_data: JSON.stringify(template.template_data, null, 2),
      preview_url: template.preview_url || '',
      is_active: template.is_active
    });
    setModalOpened(true);
  };

  const handlePreview = (template: AssetTemplate) => {
    setSelectedTemplate(template);
    setPreviewModalOpened(true);
  };

  const handleSubmit = async () => {
    try {
      // Validate form data
      if (!formData.id || !formData.name || !formData.asset_type) {
        notifications.show({
          title: 'Validation Error',
          message: 'Please fill in all required fields',
          color: 'red',
        });
        return;
      }

      // Validate JSON
      let templateData;
      try {
        templateData = JSON.parse(formData.template_data);
      } catch (error) {
        notifications.show({
          title: 'Validation Error',
          message: 'Invalid JSON format in template data',
          color: 'red',
        });
        return;
      }

      const templatePayload = {
        id: formData.id,
        name: formData.name,
        description: formData.description || null,
        asset_type: formData.asset_type as any,
        template_data: templateData,
        preview_url: formData.preview_url || null,
        is_active: formData.is_active
      };

      if (editingTemplate) {
        // Update existing template
        const { error } = await updateData('asset_templates', templatePayload, {
          column: 'id',
          value: editingTemplate.id
        });

        if (error) {
          throw error;
        }

        notifications.show({
          title: 'Success',
          message: 'Template updated successfully',
          color: 'green',
        });
      } else {
        // Create new template
        const { error } = await insertData('asset_templates', templatePayload);

        if (error) {
          throw error;
        }

        notifications.show({
          title: 'Success',
          message: 'Template created successfully',
          color: 'green',
        });
      }

      setModalOpened(false);
      fetchTemplates();
    } catch (error) {
      console.error('Error saving template:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to save template',
        color: 'red',
      });
    }
  };

  const handleDelete = (template: AssetTemplate) => {
    modals.openConfirmModal({
      title: 'Delete Template',
      children: (
        <Text size="sm">
          Are you sure you want to delete the template "{template.name}"? This action cannot be undone.
        </Text>
      ),
      labels: { confirm: 'Delete', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          const { error } = await deleteData('asset_templates', {
            column: 'id',
            value: template.id
          });

          if (error) {
            throw error;
          }

          notifications.show({
            title: 'Success',
            message: 'Template deleted successfully',
            color: 'green',
          });

          fetchTemplates();
        } catch (error) {
          console.error('Error deleting template:', error);
          notifications.show({
            title: 'Error',
            message: 'Failed to delete template',
            color: 'red',
          });
        }
      },
    });
  };

  const rows = templates.map((template) => (
    <Table.Tr key={template.id}>
      <Table.Td>
        <Group gap="sm">
          <IconTemplate size={16} />
          <div>
            <Text fw={500}>{template.name}</Text>
            <Text size="xs" c="dimmed">{template.id}</Text>
          </div>
        </Group>
      </Table.Td>
      <Table.Td>
        <Text size="sm">{template.description || 'No description'}</Text>
      </Table.Td>
      <Table.Td>
        <Badge color={getAssetTypeColor(template.asset_type)} variant="light">
          {template.asset_type}
        </Badge>
      </Table.Td>
      <Table.Td>
        <Badge color={template.is_active ? 'green' : 'red'} variant="light">
          {template.is_active ? 'Active' : 'Inactive'}
        </Badge>
      </Table.Td>
      <Table.Td>
        <Text size="xs" c="dimmed">
          {new Date(template.created_at).toLocaleDateString()}
        </Text>
      </Table.Td>
      <Table.Td>
        <Group gap="xs">
          <Tooltip label="Preview Template">
            <ActionIcon
              variant="subtle"
              color="blue"
              onClick={() => handlePreview(template)}
            >
              <IconEye size={16} />
            </ActionIcon>
          </Tooltip>
          <Tooltip label="Edit Template">
            <ActionIcon
              variant="subtle"
              color="yellow"
              onClick={() => handleEdit(template)}
            >
              <IconEdit size={16} />
            </ActionIcon>
          </Tooltip>
          <Tooltip label="Delete Template">
            <ActionIcon
              variant="subtle"
              color="red"
              onClick={() => handleDelete(template)}
            >
              <IconTrash size={16} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <>
      <Paper withBorder style={{ position: 'relative' }}>
        <LoadingOverlay visible={loading} />
        
        <Group justify="space-between" p="md">
          <div>
            <Text fw={500}>Asset Templates</Text>
            <Text size="sm" c="dimmed">
              Manage predefined templates for asset creation
            </Text>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={handleCreate}
          >
            Create Template
          </Button>
        </Group>

        {templates.length === 0 ? (
          <Alert icon={<IconAlertCircle size={16} />} color="blue" m="md">
            No asset templates found. Create your first template to get started.
          </Alert>
        ) : (
          <ScrollArea>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Template</Table.Th>
                  <Table.Th>Description</Table.Th>
                  <Table.Th>Type</Table.Th>
                  <Table.Th>Status</Table.Th>
                  <Table.Th>Created</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>{rows}</Table.Tbody>
            </Table>
          </ScrollArea>
        )}
      </Paper>

      {/* Template Form Modal */}
      <Modal
        opened={modalOpened}
        onClose={() => setModalOpened(false)}
        title={editingTemplate ? 'Edit Template' : 'Create Template'}
        size="lg"
      >
        <Stack gap="md">
          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="Template ID"
                placeholder="e.g., badge_custom_1"
                value={formData.id}
                onChange={(e) => setFormData(prev => ({ ...prev, id: e.target.value }))}
                required
                disabled={!!editingTemplate}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Asset Type"
                placeholder="Select asset type"
                data={ASSET_TYPES.map(type => ({ value: type, label: type }))}
                value={formData.asset_type}
                onChange={(value) => setFormData(prev => ({ ...prev, asset_type: value || '' }))}
                required
              />
            </Grid.Col>
          </Grid>

          <TextInput
            label="Template Name"
            placeholder="Enter template name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            required
          />

          <Textarea
            label="Description"
            placeholder="Enter template description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            minRows={2}
          />

          <TextInput
            label="Preview URL"
            placeholder="Enter preview image URL"
            value={formData.preview_url}
            onChange={(e) => setFormData(prev => ({ ...prev, preview_url: e.target.value }))}
          />

          <JsonInput
            label="Template Data"
            placeholder="Enter template configuration as JSON"
            value={formData.template_data}
            onChange={(value) => setFormData(prev => ({ ...prev, template_data: value }))}
            minRows={6}
            maxRows={12}
            required
          />

          <Select
            label="Status"
            data={[
              { value: 'true', label: 'Active' },
              { value: 'false', label: 'Inactive' }
            ]}
            value={formData.is_active.toString()}
            onChange={(value) => setFormData(prev => ({ ...prev, is_active: value === 'true' }))}
          />

          <Group justify="flex-end">
            <Button variant="subtle" onClick={() => setModalOpened(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit}>
              {editingTemplate ? 'Update' : 'Create'} Template
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Template Preview Modal */}
      <Modal
        opened={previewModalOpened}
        onClose={() => setPreviewModalOpened(false)}
        title={`Preview: ${selectedTemplate?.name}`}
        size="lg"
      >
        {selectedTemplate && (
          <Stack gap="md">
            <Card withBorder>
              <Group justify="space-between" mb="md">
                <div>
                  <Text fw={500}>{selectedTemplate.name}</Text>
                  <Text size="sm" c="dimmed">{selectedTemplate.description}</Text>
                </div>
                <Badge color={getAssetTypeColor(selectedTemplate.asset_type)} variant="light">
                  {selectedTemplate.asset_type}
                </Badge>
              </Group>

              {selectedTemplate.preview_url && (
                <div style={{ textAlign: 'center', marginBottom: '1rem' }}>
                  <Image
                    src={selectedTemplate.preview_url}
                    alt={`${selectedTemplate.name} preview`}
                    style={{ maxHeight: '300px', objectFit: 'contain' }}
                  />
                </div>
              )}

              <Text size="sm" fw={500} mb="xs">Template Configuration:</Text>
              <JsonInput
                value={JSON.stringify(selectedTemplate.template_data, null, 2)}
                readOnly
                minRows={8}
                maxRows={15}
              />
            </Card>
          </Stack>
        )}
      </Modal>
    </>
  );
}
