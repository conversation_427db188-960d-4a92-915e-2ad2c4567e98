-- Insert Plain template for all asset types
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  template_data,
  preview_url,
  is_active,
  created_at
) VALUES
-- Plain Badge template
(
  'plain_badge',
  'Plain Badge Template',
  'Minimal template that acts as a wrapper without visual effects',
  'Badge',
  '{
    "layout": "plain",
    "colors": {
      "primary": "transparent",
      "secondary": "transparent",
      "accent": "transparent",
      "text": "#374151"
    },
    "elements": {
      "background": "transparent",
      "border": "minimal"
    },
    "dimensions": {
      "width": 300,
      "height": 300
    },
    "imageArea": {
      "x": 10,
      "y": 10,
      "width": 280,
      "height": 280,
      "shape": "rectangle"
    }
  }'::jsonb,
  '/templates/plain.svg',
  true,
  NOW()
),
-- Plain Certificate template
(
  'plain_certificate',
  'Plain Certificate Template',
  'Minimal template that acts as a wrapper without visual effects',
  'Certificate',
  '{
    "layout": "plain",
    "colors": {
      "primary": "transparent",
      "secondary": "transparent",
      "accent": "transparent",
      "text": "#374151"
    },
    "elements": {
      "background": "transparent",
      "border": "minimal"
    },
    "dimensions": {
      "width": 300,
      "height": 300
    },
    "imageArea": {
      "x": 10,
      "y": 10,
      "width": 280,
      "height": 280,
      "shape": "rectangle"
    }
  }'::jsonb,
  '/templates/plain.svg',
  true,
  NOW()
),
-- Plain Ticket template
(
  'plain_ticket',
  'Plain Ticket Template',
  'Minimal template that acts as a wrapper without visual effects',
  'Ticket',
  '{
    "layout": "plain",
    "colors": {
      "primary": "transparent",
      "secondary": "transparent",
      "accent": "transparent",
      "text": "#374151"
    },
    "elements": {
      "background": "transparent",
      "border": "minimal"
    },
    "dimensions": {
      "width": 300,
      "height": 300
    },
    "imageArea": {
      "x": 10,
      "y": 10,
      "width": 280,
      "height": 280,
      "shape": "rectangle"
    }
  }'::jsonb,
  '/templates/plain.svg',
  true,
  NOW()
),
-- Plain Coupon template
(
  'plain_coupon',
  'Plain Coupon Template',
  'Minimal template that acts as a wrapper without visual effects',
  'Coupon',
  '{
    "layout": "plain",
    "colors": {
      "primary": "transparent",
      "secondary": "transparent",
      "accent": "transparent",
      "text": "#374151"
    },
    "elements": {
      "background": "transparent",
      "border": "minimal"
    },
    "dimensions": {
      "width": 300,
      "height": 300
    },
    "imageArea": {
      "x": 10,
      "y": 10,
      "width": 280,
      "height": 280,
      "shape": "rectangle"
    }
  }'::jsonb,
  '/templates/plain.svg',
  true,
  NOW()
);
