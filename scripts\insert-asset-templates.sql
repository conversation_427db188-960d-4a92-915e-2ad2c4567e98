-- Insert asset templates for the template system
-- These templates correspond to the SVG files created in /public/templates/

-- Insert Badge template
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  template_data,
  preview_url,
  is_active,
  created_at
) VALUES
(
  'badge_default',
  'Default Badge Template',
  'Standard badge template with circular design and ribbon',
  'Badge',
  '{
    "layout": "circular",
    "colors": {
      "primary": "#4F46E5",
      "secondary": "#6366F1",
      "accent": "#FCD34D",
      "text": "#FFFFFF"
    },
    "elements": {
      "background": "circle",
      "ribbon": true,
      "stars": true,
      "seal": "verified"
    },
    "dimensions": {
      "width": 300,
      "height": 300
    },
    "imageArea": {
      "x": 90,
      "y": 60,
      "width": 120,
      "height": 120,
      "shape": "circle"
    }
  }'::jsonb,
  '/templates/badge_default.svg',
  true,
  NOW()
);

-- Insert Certificate template
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  template_data,
  preview_url,
  is_active,
  created_at
) VALUES
(
  'certificate_default',
  'Default Certificate Template',
  'Professional certificate template with decorative borders',
  'Certificate',
  '{
    "layout": "landscape",
    "colors": {
      "primary": "#4F46E5",
      "secondary": "#E5E7EB",
      "accent": "#F59E0B",
      "text": "#374151"
    },
    "elements": {
      "border": "decorative",
      "header": true,
      "seal": true,
      "signature": false
    },
    "dimensions": {
      "width": 400,
      "height": 300
    },
    "imageArea": {
      "x": 50,
      "y": 60,
      "width": 300,
      "height": 140,
      "shape": "rectangle"
    }
  }'::jsonb,
  '/templates/certificate_default.svg',
  true,
  NOW()
);

-- Insert Ticket template
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  template_data,
  preview_url,
  is_active,
  created_at
) VALUES
(
  'ticket_default',
  'Default Ticket Template',
  'Event ticket template with perforated edge and stub',
  'Ticket',
  '{
    "layout": "horizontal",
    "colors": {
      "primary": "#F59E0B",
      "secondary": "#FEF3C7",
      "accent": "#D97706",
      "text": "#92400E"
    },
    "elements": {
      "perforation": true,
      "stub": true,
      "qrCode": true,
      "serialNumber": true
    },
    "dimensions": {
      "width": 350,
      "height": 200
    },
    "imageArea": {
      "x": 30,
      "y": 30,
      "width": 120,
      "height": 80,
      "shape": "rectangle"
    }
  }'::jsonb,
  '/templates/ticket_default.svg',
  true,
  NOW()
);

-- Insert Coupon template
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  template_data,
  preview_url,
  is_active,
  created_at
) VALUES
(
  'coupon_default',
  'Default Coupon Template',
  'Discount coupon template with wavy borders and scissors marks',
  'Coupon',
  '{
    "layout": "horizontal",
    "colors": {
      "primary": "#EF4444",
      "secondary": "#FEF2F2",
      "accent": "#DC2626",
      "text": "#7F1D1D"
    },
    "elements": {
      "wavyBorder": true,
      "scissors": true,
      "cutLine": true,
      "discount": "percentage"
    },
    "dimensions": {
      "width": 400,
      "height": 250
    },
    "imageArea": {
      "x": 50,
      "y": 70,
      "width": 120,
      "height": 80,
      "shape": "rectangle"
    }
  }'::jsonb,
  '/templates/coupon_default.svg',
  true,
  NOW()
);

-- Insert additional Badge template (Premium style)
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  template_data,
  preview_url,
  is_active,
  created_at
) VALUES
(
  'badge_premium',
  'Premium Badge Template',
  'Premium badge template with gold accents and enhanced design',
  'Badge',
  '{
    "layout": "circular",
    "colors": {
      "primary": "#7C2D12",
      "secondary": "#DC2626",
      "accent": "#FCD34D",
      "text": "#FFFFFF"
    },
    "elements": {
      "background": "circle",
      "ribbon": true,
      "stars": true,
      "seal": "premium",
      "gradient": true
    },
    "dimensions": {
      "width": 300,
      "height": 300
    },
    "imageArea": {
      "x": 90,
      "y": 60,
      "width": 120,
      "height": 120,
      "shape": "circle"
    }
  }'::jsonb,
  '/templates/badge_default.svg',
  true,
  NOW()
);

-- Insert additional Certificate template (Academic style)
INSERT INTO asset_templates (
  id,
  name,
  description,
  asset_type,
  template_data,
  preview_url,
  is_active,
  created_at
) VALUES
(
  'certificate_academic',
  'Academic Certificate Template',
  'Academic certificate template with institutional styling',
  'Certificate',
  '{
    "layout": "portrait",
    "colors": {
      "primary": "#1E40AF",
      "secondary": "#DBEAFE",
      "accent": "#059669",
      "text": "#1F2937"
    },
    "elements": {
      "border": "academic",
      "header": true,
      "seal": true,
      "signature": true,
      "crest": true
    },
    "dimensions": {
      "width": 400,
      "height": 300
    },
    "imageArea": {
      "x": 50,
      "y": 60,
      "width": 300,
      "height": 140,
      "shape": "rectangle"
    }
  }'::jsonb,
  '/templates/certificate_default.svg',
  true,
  NOW()
);

-- Verify the inserted templates
SELECT 
  id,
  name,
  asset_type,
  is_active,
  created_at
FROM asset_templates 
ORDER BY asset_type, name;
