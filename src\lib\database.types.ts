export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      // Define your tables here
      profiles: {
        Row: {
          id: string
          created_at: string
          updated_at: string | null
          full_name: string | null
          avatar_url: string | null
          email: string | null
          disabled: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string | null
          full_name?: string | null
          avatar_url?: string | null
          email?: string | null
          disabled?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string | null
          full_name?: string | null
          avatar_url?: string | null
          email?: string | null
          disabled?: boolean
        }
      }
      // New people table for ODude Name data with individual columns
      contact: {
        Row: {
          timestamp: string
          name: string
          image: string | null
          description: string | null
          uri: string | null
          profile: string | null
          email: string | null
          website: string | null
          phone: string | null
          tg_bot: string | null
          notes: Json | null

          web2: string | null
          web3: string | null
          links: Json | null
          images: Json | null
          social: Json | null
          crypto: Json | null
          extra: Json | null
          profile_email: string | null
          minted: string | null
          disabled: boolean
        }
        Insert: {
          timestamp?: string
          name: string
          image?: string | null
          description?: string | null
          uri?: string | null
          profile?: string | null
          email?: string | null
          website?: string | null
          phone?: string | null
          tg_bot?: string | null
          notes?: Json | null

          web2?: string | null
          web3?: string | null
          links?: Json | null
          images?: Json | null
          social?: Json | null
          crypto?: Json | null
          extra?: Json | null
          profile_email?: string | null
          minted?: string | null
          disabled?: boolean
        }
        Update: {
          timestamp?: string
          name?: string
          image?: string | null
          description?: string | null
          uri?: string | null
          profile?: string | null
          email?: string | null
          website?: string | null
          phone?: string | null
          tg_bot?: string | null
          notes?: Json | null

          web2?: string | null
          web3?: string | null
          links?: Json | null
          images?: Json | null
          social?: Json | null
          crypto?: Json | null
          extra?: Json | null
          profile_email?: string | null
          minted?: string | null
          disabled?: boolean
        }
      }
      bookmark: {
        Row: {
          id: number;
          created_at: string;
          contact_name: string;
          contact_email: string;
        };
        Insert: {
          id?: number;
          created_at?: string;
          contact_name: string;
          contact_email: string;
        };
        Update: {
          id?: number;
          created_at?: string;
          contact_name?: string;
          contact_email?: string;
        };
      }
      settings: {
        Row: {
          email: string;
          max_contact_limit: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          email: string;
          max_contact_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          email?: string;
          max_contact_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
      }
      user_points: {
        Row: {
          email: string;
          points: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          email: string;
          points?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          email?: string;
          points?: number;
          created_at?: string;
          updated_at?: string;
        };
      }
      transaction_logs: {
        Row: {
          id: number;
          email: string;
          transaction_type: string;
          points_change: number;
          points_before: number;
          points_after: number;
          description: string | null;
          reference_id: string | null;
          from_email: string | null;
          to_email: string | null;
          created_at: string;
        };
        Insert: {
          id?: number;
          email: string;
          transaction_type: string;
          points_change: number;
          points_before: number;
          points_after: number;
          description?: string | null;
          reference_id?: string | null;
          from_email?: string | null;
          to_email?: string | null;
          created_at?: string;
        };
        Update: {
          id?: number;
          email?: string;
          transaction_type?: string;
          points_change?: number;
          points_before?: number;
          points_after?: number;
          description?: string | null;
          reference_id?: string | null;
          from_email?: string | null;
          to_email?: string | null;
          created_at?: string;
        };
      }
      // Assets system tables
      assets: {
        Row: {
          id: string;
          title: string;
          description: string | null;
          asset_type: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
          image_url: string;
          template_id: string | null;
          issuer_odude_name: string;
          issuer_email: string;
          expiry_date: string | null;
          metadata: Json;
          created_at: string;
          updated_at: string;
          is_deleted: boolean;
        };
        Insert: {
          id?: string;
          title: string;
          description?: string | null;
          asset_type: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
          image_url: string;
          template_id?: string | null;
          issuer_odude_name: string;
          issuer_email: string;
          expiry_date?: string | null;
          metadata?: Json;
          created_at?: string;
          updated_at?: string;
          is_deleted?: boolean;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string | null;
          asset_type?: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
          image_url?: string;
          template_id?: string | null;
          issuer_odude_name?: string;
          issuer_email?: string;
          expiry_date?: string | null;
          metadata?: Json;
          created_at?: string;
          updated_at?: string;
          is_deleted?: boolean;
        };
      }
      asset_transfers: {
        Row: {
          id: string;
          asset_id: string;
          from_odude_name: string;
          from_email: string;
          to_odude_name: string;
          to_email: string | null;
          status: 'pending' | 'approved' | 'declined' | 'hidden';
          transferred_at: string;
          responded_at: string | null;
          response_note: string | null;
        };
        Insert: {
          id?: string;
          asset_id: string;
          from_odude_name: string;
          from_email: string;
          to_odude_name: string;
          to_email?: string | null;
          status?: 'pending' | 'approved' | 'declined' | 'hidden';
          transferred_at?: string;
          responded_at?: string | null;
          response_note?: string | null;
        };
        Update: {
          id?: string;
          asset_id?: string;
          from_odude_name?: string;
          from_email?: string;
          to_odude_name?: string;
          to_email?: string | null;
          status?: 'pending' | 'approved' | 'declined' | 'hidden';
          transferred_at?: string;
          responded_at?: string | null;
          response_note?: string | null;
        };
      }
      asset_templates: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          asset_type: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
          template_data: Json;
          preview_url: string | null;
          is_active: boolean;
          created_at: string;
        };
        Insert: {
          id: string;
          name: string;
          description?: string | null;
          asset_type: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
          template_data?: Json;
          preview_url?: string | null;
          is_active?: boolean;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          asset_type?: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
          template_data?: Json;
          preview_url?: string | null;
          is_active?: boolean;
          created_at?: string;
        };
      }
      // Add more tables as needed
    }
    Views: {
      // Define your views here
      assets_with_stats: {
        Row: {
          id: string;
          title: string;
          description: string | null;
          asset_type: 'Badge' | 'Certificate' | 'Ticket' | 'Coupon';
          image_url: string;
          template_id: string | null;
          issuer_odude_name: string;
          issuer_email: string;
          expiry_date: string | null;
          metadata: Json;
          created_at: string;
          updated_at: string;
          is_deleted: boolean;
          total_transfers: number;
          approved_transfers: number;
          pending_transfers: number;
        };
      }
    }
    Functions: {
      // Define your functions here
    }
    Enums: {
      // Define your enums here
    }
  }
}
