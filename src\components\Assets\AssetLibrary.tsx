'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  Table,
  Group,
  Text,
  ActionIcon,
  Badge,
  Image,
  Button,
  Stack,
  Paper,
  Alert,
  LoadingOverlay,
  Modal,
  TextInput,
  Menu,
  Tooltip,
  Card,
  Grid,
  Box
} from '@mantine/core';
import {
  IconEdit,
  IconTrash,
  IconSend,
  IconEye,
  IconDots,
  IconAlertCircle,
  IconPhoto,
  IconCalendar,
  IconUser,
  IconTag,
  IconHistory,
  IconX
} from '@tabler/icons-react';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import { fetchData, insertData, updateData, deleteData } from '../../lib/supabase';
import { 
  Asset, 
  AssetWithStats,
  formatAssetForDisplay, 
  canManageAsset,
  isAssetExpired,
  getAssetTypeColor 
} from '../../lib/assets-utils';

interface AssetLibraryProps {
  refreshTrigger?: number;
}

interface AssetSendModalProps {
  asset: Asset;
  opened: boolean;
  onClose: () => void;
  onSent: () => void;
}

function AssetSendModal({ asset, opened, onClose, onSent }: AssetSendModalProps) {
  const [recipientName, setRecipientName] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSend = async () => {
    if (!recipientName.trim()) {
      notifications.show({
        title: 'Error',
        message: 'Please enter a recipient ODude name',
        color: 'red',
      });
      return;
    }

    try {
      setLoading(true);

      // Use the API endpoint for asset transfer
      const response = await fetch('/api/assets/transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          asset_id: asset.id,
          to_odude_name: recipientName.trim()
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send asset');
      }

      notifications.show({
        title: 'Success',
        message: result.message || `Asset sent to ${recipientName}`,
        color: 'green',
      });

      setRecipientName('');
      onSent();
      onClose();

    } catch (error) {
      console.error('Error sending asset:', error);
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to send asset. Please try again.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal opened={opened} onClose={onClose} title="Send Asset" centered>
      <Stack gap="md">
        <Card withBorder p="md">
          <Group>
            <Image
              src={asset.image_url}
              alt={asset.title}
              width={60}
              height={60}
              fit="cover"
              radius="md"
            />
            <div>
              <Text fw={500}>{asset.title}</Text>
              <Badge color={getAssetTypeColor(asset.asset_type)} variant="light" size="sm">
                {asset.asset_type}
              </Badge>
            </div>
          </Group>
        </Card>

        <TextInput
          label="Recipient ODude Name"
          placeholder="Enter ODude name (e.g., user@me)"
          value={recipientName}
          onChange={(e) => setRecipientName(e.target.value)}
          required
        />

        <Group justify="flex-end">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSend} loading={loading} leftSection={<IconSend size={16} />}>
            Send Asset
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}

export function AssetLibrary({ refreshTrigger }: AssetLibraryProps) {
  const { data: session } = useSession();
  const [assets, setAssets] = useState<AssetWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [sendModalOpened, setSendModalOpened] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [viewPublicModalOpened, setViewPublicModalOpened] = useState(false);
  const [publicViewAsset, setPublicViewAsset] = useState<Asset | null>(null);
  const [transactionsModalOpened, setTransactionsModalOpened] = useState(false);
  const [transactionsAsset, setTransactionsAsset] = useState<Asset | null>(null);
  const [transactions, setTransactions] = useState<any[]>([]);

  useEffect(() => {
    if (session?.user?.email) {
      fetchAssets();
    }
  }, [session, refreshTrigger]);

  const fetchAssets = async () => {
    if (!session?.user?.email) return;

    try {
      setLoading(true);
      
      // Fetch assets with stats using the view
      const { data, error } = await fetchData('assets_with_stats', {
        select: '*',
        filter: [
          { column: 'issuer_email', value: session.user.email },
          { column: 'is_deleted', value: false }
        ]
      });

      if (!error && data) {
        const assetsArray = Array.isArray(data) ? data : [data];
        setAssets(assetsArray);
      }
    } catch (error) {
      console.error('Error fetching assets:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load assets',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSendAsset = (asset: Asset) => {
    setSelectedAsset(asset);
    setSendModalOpened(true);
  };

  const handleViewPublic = (asset: Asset) => {
    setPublicViewAsset(asset);
    setViewPublicModalOpened(true);
  };

  const handleEditAsset = (asset: Asset) => {
    // TODO: Implement edit modal - this should be editable
    notifications.show({
      title: 'Edit Asset',
      message: 'Asset editing functionality needs to be implemented',
      color: 'orange',
    });
  };

  const handleViewTransactions = async (asset: Asset) => {
    setTransactionsAsset(asset);
    setTransactionsModalOpened(true);

    try {
      // Fetch asset transfers for this asset
      const { data, error } = await fetchData('asset_transfers', {
        select: `
          *,
          assets (title, asset_type)
        `,
        filter: [{ column: 'asset_id', value: asset.id }]
      });

      if (!error && data) {
        const transfersArray = Array.isArray(data) ? data : [data];
        setTransactions(transfersArray);
      } else {
        setTransactions([]);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setTransactions([]);
    }
  };

  const handleRevokeTransfer = async (transferId: string) => {
    try {
      // Delete the transfer record
      const { error } = await deleteData('asset_transfers', transferId);

      if (error) {
        notifications.show({
          title: 'Error',
          message: 'Failed to revoke asset transfer',
          color: 'red',
        });
        return;
      }

      notifications.show({
        title: 'Success',
        message: 'Asset transfer revoked successfully',
        color: 'green',
      });

      // Refresh transactions
      if (transactionsAsset) {
        handleViewTransactions(transactionsAsset);
      }

      // Refresh assets list
      fetchAssets();
    } catch (error) {
      console.error('Error revoking transfer:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to revoke asset transfer',
        color: 'red',
      });
    }
  };

  const handleDeleteAsset = (asset: Asset) => {
    modals.openConfirmModal({
      title: 'Delete Asset',
      children: (
        <Text size="sm">
          Are you sure you want to delete "{asset.title}"? This will permanently remove the asset 
          from everywhere, including all copies that have been sent to others.
        </Text>
      ),
      labels: { confirm: 'Delete', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          const { error } = await updateData(
            'assets',
            { is_deleted: true },
            { column: 'id', value: asset.id }
          );

          if (error) throw error;

          notifications.show({
            title: 'Success',
            message: 'Asset deleted successfully',
            color: 'green',
          });

          fetchAssets();
        } catch (error) {
          console.error('Error deleting asset:', error);
          notifications.show({
            title: 'Error',
            message: 'Failed to delete asset',
            color: 'red',
          });
        }
      },
    });
  };

  if (loading) {
    return (
      <Paper withBorder p="md" style={{ position: 'relative', minHeight: 200 }}>
        <LoadingOverlay visible />
      </Paper>
    );
  }

  if (assets.length === 0) {
    return (
      <Paper withBorder p="xl" style={{ textAlign: 'center' }}>
        <IconPhoto size={48} color="gray" style={{ margin: '0 auto 16px' }} />
        <Text size="lg" fw={500} mb="xs">No Assets Created</Text>
        <Text c="dimmed" mb="md">
          You haven't created any assets yet. Create your first asset to get started.
        </Text>
      </Paper>
    );
  }

  const rows = assets.map((asset) => {
    const formattedAsset = formatAssetForDisplay(asset);
    const expired = isAssetExpired(asset);

    return (
      <Table.Tr key={asset.id} style={{ opacity: expired ? 0.6 : 1 }}>
        <Table.Td>
          <Group gap="sm">
            <Image
              src={asset.image_url}
              alt={asset.title}
              width={40}
              height={40}
              fit="cover"
              radius="sm"
            />
            <div>
              <Text fw={500} size="sm">{asset.title}</Text>
              <Text size="xs" c="dimmed">{asset.description}</Text>
            </div>
          </Group>
        </Table.Td>
        
        <Table.Td>
          <Badge color={formattedAsset.typeColor} variant="light" size="sm">
            {asset.asset_type}
          </Badge>
        </Table.Td>
        
        <Table.Td>
          <Text size="sm">{formattedAsset.formattedCreatedAt}</Text>
        </Table.Td>
        
        <Table.Td>
          {asset.expiry_date ? (
            <Group gap="xs">
              <Text size="sm" c={expired ? 'red' : 'dimmed'}>
                {formattedAsset.formattedExpiryDate}
              </Text>
              {expired && <Badge color="red" size="xs">Expired</Badge>}
            </Group>
          ) : (
            <Text size="sm" c="dimmed">No expiry</Text>
          )}
        </Table.Td>
        
        <Table.Td>
          <Group gap="xs">
            <Tooltip label="Total transfers">
              <Badge variant="outline" size="sm">{asset.total_transfers}</Badge>
            </Tooltip>
            <Tooltip label="Approved">
              <Badge color="green" variant="outline" size="sm">{asset.approved_transfers}</Badge>
            </Tooltip>
            <Tooltip label="Pending">
              <Badge color="orange" variant="outline" size="sm">{asset.pending_transfers}</Badge>
            </Tooltip>
          </Group>
        </Table.Td>
        
        <Table.Td>
          <Group gap="xs">
            <Tooltip label="Send Asset">
              <ActionIcon
                variant="light"
                color="blue"
                onClick={() => handleSendAsset(asset)}
                disabled={expired}
              >
                <IconSend size={16} />
              </ActionIcon>
            </Tooltip>
            
            <Menu shadow="md" width={200}>
              <Menu.Target>
                <ActionIcon variant="light" color="gray">
                  <IconDots size={16} />
                </ActionIcon>
              </Menu.Target>
              
              <Menu.Dropdown>
                <Menu.Item
                  leftSection={<IconEdit size={14} />}
                  onClick={() => handleEditAsset(asset)}
                >
                  Edit Asset
                </Menu.Item>
                <Menu.Item
                  leftSection={<IconEye size={14} />}
                  onClick={() => handleViewPublic(asset)}
                >
                  View Public
                </Menu.Item>
                <Menu.Item
                  leftSection={<IconHistory size={14} />}
                  onClick={() => handleViewTransactions(asset)}
                >
                  View Transactions
                </Menu.Item>
                <Menu.Divider />
                <Menu.Item
                  leftSection={<IconTrash size={14} />}
                  color="red"
                  onClick={() => handleDeleteAsset(asset)}
                >
                  Delete Asset
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Group>
        </Table.Td>
      </Table.Tr>
    );
  });

  return (
    <Stack gap="md">
      {/* Summary Stats */}
      <Grid>
        <Grid.Col span={3}>
          <Paper withBorder p="md" style={{ textAlign: 'center' }}>
            <Text size="xl" fw={700} c="blue">{assets.length}</Text>
            <Text size="sm" c="dimmed">Total Assets</Text>
          </Paper>
        </Grid.Col>
        <Grid.Col span={3}>
          <Paper withBorder p="md" style={{ textAlign: 'center' }}>
            <Text size="xl" fw={700} c="green">
              {assets.reduce((sum, asset) => sum + asset.approved_transfers, 0)}
            </Text>
            <Text size="sm" c="dimmed">Approved Transfers</Text>
          </Paper>
        </Grid.Col>
        <Grid.Col span={3}>
          <Paper withBorder p="md" style={{ textAlign: 'center' }}>
            <Text size="xl" fw={700} c="orange">
              {assets.reduce((sum, asset) => sum + asset.pending_transfers, 0)}
            </Text>
            <Text size="sm" c="dimmed">Pending Transfers</Text>
          </Paper>
        </Grid.Col>
        <Grid.Col span={3}>
          <Paper withBorder p="md" style={{ textAlign: 'center' }}>
            <Text size="xl" fw={700} c="red">
              {assets.filter(asset => isAssetExpired(asset)).length}
            </Text>
            <Text size="sm" c="dimmed">Expired Assets</Text>
          </Paper>
        </Grid.Col>
      </Grid>

      {/* Assets Table */}
      <Paper withBorder>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Asset</Table.Th>
              <Table.Th>Type</Table.Th>
              <Table.Th>Created</Table.Th>
              <Table.Th>Expires</Table.Th>
              <Table.Th>Transfers</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>
      </Paper>

      {/* Send Modal */}
      {selectedAsset && (
        <AssetSendModal
          asset={selectedAsset}
          opened={sendModalOpened}
          onClose={() => {
            setSendModalOpened(false);
            setSelectedAsset(null);
          }}
          onSent={fetchAssets}
        />
      )}

      {/* View Public Modal */}
      {publicViewAsset && (
        <Modal
          opened={viewPublicModalOpened}
          onClose={() => {
            setViewPublicModalOpened(false);
            setPublicViewAsset(null);
          }}
          title={`${publicViewAsset.issuer_odude_name} - Public Assets`}
          size="xl"
          centered
        >
          <iframe
            src={`/profile/assets/${publicViewAsset.issuer_odude_name}`}
            style={{
              width: '100%',
              height: '600px',
              border: 'none',
              borderRadius: '8px'
            }}
            title={`${publicViewAsset.issuer_odude_name} Assets`}
          />
        </Modal>
      )}

      {/* Transactions Modal */}
      {transactionsAsset && (
        <Modal
          opened={transactionsModalOpened}
          onClose={() => {
            setTransactionsModalOpened(false);
            setTransactionsAsset(null);
            setTransactions([]);
          }}
          title={`Asset Transactions - ${transactionsAsset.title}`}
          size="xl"
          centered
        >
          <Stack gap="md">
            {transactions.length === 0 ? (
              <Alert icon={<IconAlertCircle size={16} />} color="blue">
                No transfers found for this asset.
              </Alert>
            ) : (
              <Table>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Recipient</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Transferred At</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {transactions.map((transfer) => (
                    <Table.Tr key={transfer.id}>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">{transfer.to_odude_name}</Text>
                          <Text size="xs" c="dimmed">{transfer.to_email}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          color={
                            transfer.status === 'approved' ? 'green' :
                            transfer.status === 'pending' ? 'orange' :
                            transfer.status === 'declined' ? 'red' : 'gray'
                          }
                          variant="light"
                        >
                          {transfer.status}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {new Date(transfer.transferred_at).toLocaleDateString()}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Tooltip label="Revoke Transfer">
                          <ActionIcon
                            variant="light"
                            color="red"
                            onClick={() => {
                              modals.openConfirmModal({
                                title: 'Revoke Asset Transfer',
                                children: (
                                  <Text size="sm">
                                    Are you sure you want to revoke this asset transfer to {transfer.to_odude_name}?
                                    This will permanently remove the asset from their collection.
                                  </Text>
                                ),
                                labels: { confirm: 'Revoke', cancel: 'Cancel' },
                                confirmProps: { color: 'red' },
                                onConfirm: () => handleRevokeTransfer(transfer.id),
                              });
                            }}
                          >
                            <IconX size={16} />
                          </ActionIcon>
                        </Tooltip>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            )}
          </Stack>
        </Modal>
      )}
    </Stack>
  );
}
